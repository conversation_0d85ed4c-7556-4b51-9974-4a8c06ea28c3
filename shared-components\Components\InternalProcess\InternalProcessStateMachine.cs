using shared.Components.InternalProcess.Enums;
using shared.Components.InternalProcess.Interfaces;
using shared.Components.InternalProcess.Models;
using shared.Models.Interfaces;

namespace shared.Components.InternalProcess
{
    /// <summary>
    /// A generic state machine implementation for internal processes.
    /// Supports state transitions, task execution, and state persistence.
    /// </summary>
    /// <typeparam name="TState">The type of the state object that implements IStateful</typeparam>
    /// <typeparam name="TObject">The type of the payload object</typeparam>
    public class InternalProcessStateMachine<TState, TObject> : IInternalProcessStateMachine<TState, TObject> where TState : IStateful<TState>
    {
        private readonly Dictionary<TState, StateConfiguration<TState, TObject>> _stateConfigurations;
        private InternalProcessStateData<TState, TObject> _stateData;

        /// <summary>
        /// Gets the current state data of the state machine.
        /// </summary>
        public InternalProcessStateData<TState, TObject> StateData => _stateData;

        /// <summary>
        /// Gets the current state of the stateful object.
        /// </summary>
        public TState CurrentState => _stateData.StatefulObject;

        /// <summary>
        /// Gets the current internal process state.
        /// </summary>
        public InternalProcessState CurrentInternalProcessState => _stateData.InternalProcessState;

        /// <summary>
        /// Gets the current payload.
        /// </summary>
        public TObject Payload => _stateData.Payload;

        /// <summary>
        /// Initializes a new instance of the InternalProcessStateMachine.
        /// </summary>
        /// <param name="initialState">The initial state</param>
        /// <param name="payload">The payload object</param>
        /// <param name="initialInternalProcessState">The initial internal process state</param>
        public InternalProcessStateMachine(TState initialState, TObject payload, InternalProcessState initialInternalProcessState = InternalProcessState.Finished)
        {
            _stateConfigurations = new Dictionary<TState, StateConfiguration<TState, TObject>>();
            _stateData = new InternalProcessStateData<TState, TObject>
            {
                StatefulObject = initialState,
                Payload = payload,
                InternalProcessState = initialInternalProcessState,
                LastUpdated = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Initializes a new instance of the InternalProcessStateMachine from existing state data.
        /// </summary>
        /// <param name="stateData">The existing state data</param>
        public InternalProcessStateMachine(InternalProcessStateData<TState, TObject> stateData)
        {
            _stateConfigurations = new Dictionary<TState, StateConfiguration<TState, TObject>>();
            _stateData = stateData;
        }

        /// <summary>
        /// Configures a state in the state machine.
        /// </summary>
        /// <param name="state">The state to configure</param>
        /// <returns>A state configuration object for method chaining</returns>
        public StateConfiguration<TState, TObject> ConfigureState(TState state)
        {
            if (!_stateConfigurations.ContainsKey(state))
            {
                _stateConfigurations[state] = new StateConfiguration<TState, TObject>(state);
            }
            return _stateConfigurations[state];
        }

        /// <summary>
        /// Forces the state machine to a specific state and internal process state.
        /// </summary>
        /// <param name="newState">The new state</param>
        /// <param name="newInternalProcessState">The new internal process state</param>
        public void ForceState(TState newState, InternalProcessState newInternalProcessState)
        {
            _stateData.StatefulObject = newState;
            _stateData.InternalProcessState = newInternalProcessState;
            _stateData.LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// Updates the payload object.
        /// </summary>
        /// <param name="newPayload">The new payload</param>
        public void UpdatePayload(TObject newPayload)
        {
            _stateData.Payload = newPayload;
            _stateData.LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// Runs the task associated with the current state.
        /// </summary>
        /// <returns>The new internal process state returned by the task</returns>
        /// <exception cref="InvalidOperationException">Thrown when no task is configured for the current state</exception>
        public async Task<InternalProcessState> RunStateTask()
        {
            var currentState = _stateData.StatefulObject;

            if (!_stateConfigurations.ContainsKey(currentState))
            {
                throw new InvalidOperationException($"No configuration found for state: {currentState}");
            }

            var stateConfig = _stateConfigurations[currentState];
            if (stateConfig.StateTask == null)
            {
                throw new InvalidOperationException($"No task configured for state: {currentState}");
            }

            try
            {
                var newInternalProcessState = await stateConfig.StateTask(_stateData.Payload, _stateData.InternalProcessState);
                _stateData.InternalProcessState = newInternalProcessState;
                _stateData.LastUpdated = DateTime.UtcNow;
                _stateData.ErrorMessage = null; // Clear any previous error

                return newInternalProcessState;
            }
            catch (Exception ex)
            {
                _stateData.InternalProcessState = InternalProcessState.Failed;
                _stateData.ErrorMessage = ex.Message;
                _stateData.LastUpdated = DateTime.UtcNow;
                throw;
            }
        }

        /// <summary>
        /// Attempts to transition to a new state based on the current internal process state.
        /// </summary>
        /// <returns>True if a transition was made, false if no valid transition exists</returns>
        public bool TryTransition()
        {
            var currentState = _stateData.StatefulObject;
            var currentInternalProcessState = _stateData.InternalProcessState;

            if (!_stateConfigurations.ContainsKey(currentState))
            {
                return false;
            }

            var stateConfig = _stateConfigurations[currentState];
            var transition = stateConfig.Transitions.FirstOrDefault(t => t.Trigger.Equals(currentInternalProcessState));

            if (transition != null)
            {
                _stateData.StatefulObject = transition.DestinationState;
                _stateData.LastUpdated = DateTime.UtcNow;
                return true;
            }

            return false;
        }

        /// <summary>
        /// Executes the current state task and attempts to transition based on the result.
        /// </summary>
        /// <returns>True if a transition was made after task execution</returns>
        public async Task<bool> ExecuteAndTransition()
        {
            await RunStateTask();
            return TryTransition();
        }

        /// <summary>
        /// Sets an error message and updates the internal process state to Failed.
        /// </summary>
        /// <param name="errorMessage">The error message</param>
        public void SetError(string errorMessage)
        {
            _stateData.ErrorMessage = errorMessage;
            _stateData.InternalProcessState = InternalProcessState.Failed;
            _stateData.LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// Increments the retry count and sets the internal process state to Retry.
        /// </summary>
        public void IncrementRetry()
        {
            _stateData.RetryCount++;
            _stateData.InternalProcessState = InternalProcessState.Retry;
            _stateData.LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// Gets all valid transitions from the current state.
        /// </summary>
        /// <returns>List of valid transitions</returns>
        public List<StateTransition<TState>> GetValidTransitions()
        {
            var currentState = _stateData.StatefulObject;

            if (_stateConfigurations.ContainsKey(currentState))
            {
                return _stateConfigurations[currentState].Transitions;
            }

            return new List<StateTransition<TState>>();
        }

        /// <summary>
        /// Checks if a transition is valid from the current state with the given trigger.
        /// </summary>
        /// <param name="trigger">The trigger to check</param>
        /// <returns>True if the transition is valid</returns>
        public bool IsTransitionValid(InternalProcessState trigger)
        {
            var currentState = _stateData.StatefulObject;

            if (_stateConfigurations.ContainsKey(currentState))
            {
                return _stateConfigurations[currentState].Transitions.Any(t => t.Trigger.Equals(trigger));
            }

            return false;
        }
    }
}
