# InternalProcess State Machine

A generic state machine implementation designed for managing internal processes in controllers. This component provides a flexible and serializable state machine that can handle state transitions, task execution, and state persistence.

## Features

1. **State Data Management**: Stores internal process state, IStateful object, and payload
2. **State Machine Functionality**: Define states and permitted transitions with triggers
3. **Task Execution**: Each state can have an associated task that returns a new internal process state
4. **State Forcing**: Ability to load/force specific states for configuration restoration
5. **Serializable State Data**: Single serializable class for easy persistence and message passing

## Core Components

### InternalProcessState Enum
- `Finished`: Process has completed
- `Failed`: Process has failed
- `Succeeded`: Process completed successfully
- `Retry`: Process should be retried

### InternalProcessStateData<TState, TObject>
Serializable container for all state machine data:
- `InternalProcessState`: Current internal process state
- `StatefulObject`: IStateful object tracking the process state
- `Payload`: The actual object being processed
- `LastUpdated`: Timestamp of last update
- `ErrorMessage`: Optional error message
- `RetryCount`: Number of retry attempts

### StateTransition<TState>
Defines a transition from one state to another:
- `Trigger`: The internal process state that triggers the transition
- `DestinationState`: The target state to transition to

### StateConfiguration<TState, TObject>
Configuration for a specific state:
- `State`: The state this configuration applies to
- `Transitions`: List of permitted transitions
- `StateTask`: Task to execute when in this state

## Usage Example

```csharp
// Define your state enum that implements IStateful
public enum ProcessState
{
    Initial,
    Processing,
    Completed,
    Error
}

public class MyState : IStateful<ProcessState>
{
    public ProcessState Status { get; set; }
}

// Define your payload
public class MyPayload
{
    public string Data { get; set; }
    public int Value { get; set; }
}

// Create the state machine
var initialState = new MyState { Status = ProcessState.Initial };
var payload = new MyPayload { Data = "test", Value = 42 };

var stateMachine = new InternalProcessStateMachine<MyState, MyPayload>(
    initialState, 
    payload, 
    InternalProcessState.Finished
);

// Configure states and transitions
stateMachine.ConfigureState(new MyState { Status = ProcessState.Initial })
    .AddTransition(InternalProcessState.Succeeded, new MyState { Status = ProcessState.Processing })
    .AddTransition(InternalProcessState.Failed, new MyState { Status = ProcessState.Error })
    .SetTask(async (payload, currentInternalState) =>
    {
        // Your task logic here
        await Task.Delay(100);
        return InternalProcessState.Succeeded;
    });

stateMachine.ConfigureState(new MyState { Status = ProcessState.Processing })
    .AddTransition(InternalProcessState.Succeeded, new MyState { Status = ProcessState.Completed })
    .AddTransition(InternalProcessState.Failed, new MyState { Status = ProcessState.Error })
    .SetTask(async (payload, currentInternalState) =>
    {
        // Processing logic
        return InternalProcessState.Succeeded;
    });

// Execute state tasks and transitions
var newInternalState = await stateMachine.RunStateTask();
bool transitioned = stateMachine.TryTransition();

// Or execute and transition in one call
bool executed = await stateMachine.ExecuteAndTransition();

// Access state data for serialization/persistence
var stateData = stateMachine.StateData;
```

## State Persistence

The `InternalProcessStateData` class is fully serializable and can be used with:
- JSON serialization for message services
- DynamoDB persistence with proper converters
- Any other serialization mechanism

```csharp
// Serialize state data
var json = JsonSerializer.Serialize(stateMachine.StateData);

// Restore from state data
var restoredStateData = JsonSerializer.Deserialize<InternalProcessStateData<MyState, MyPayload>>(json);
var restoredStateMachine = new InternalProcessStateMachine<MyState, MyPayload>(restoredStateData);
```

## Error Handling

The state machine provides built-in error handling:
- Automatic error state setting on task exceptions
- Error message capture
- Retry count tracking

```csharp
// Handle errors
try
{
    await stateMachine.RunStateTask();
}
catch (Exception ex)
{
    // State machine automatically sets Failed state and error message
    Console.WriteLine($"Error: {stateMachine.StateData.ErrorMessage}");
}

// Manual error setting
stateMachine.SetError("Custom error message");

// Retry handling
stateMachine.IncrementRetry();
```

## Integration with Controllers

This state machine is designed to work seamlessly with controllers for managing internal processes:

```csharp
[ApiController]
public class ProcessController : ControllerBase
{
    private readonly IInternalProcessStateMachine<MyState, MyPayload> _stateMachine;

    public ProcessController(IInternalProcessStateMachine<MyState, MyPayload> stateMachine)
    {
        _stateMachine = stateMachine;
    }

    [HttpPost("execute")]
    public async Task<IActionResult> ExecuteProcess()
    {
        try
        {
            await _stateMachine.ExecuteAndTransition();
            return Ok(_stateMachine.StateData);
        }
        catch (Exception ex)
        {
            return BadRequest(new { error = ex.Message, state = _stateMachine.StateData });
        }
    }
}
```
