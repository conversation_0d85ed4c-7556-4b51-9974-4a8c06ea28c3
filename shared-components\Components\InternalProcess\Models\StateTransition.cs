using shared.Components.InternalProcess.Enums;
using shared.Models.Interfaces;

namespace shared.Components.InternalProcess.Models
{
    /// <summary>
    /// Represents a state transition in the state machine.
    /// </summary>
    /// <typeparam name="TState">The type of the state object that implements IStateful</typeparam>
    public class StateTransition<TState> where TState : IStateful<TState>
    {
        /// <summary>
        /// The trigger that causes this transition (internal process state).
        /// </summary>
        public InternalProcessState Trigger { get; set; }

        /// <summary>
        /// The destination state to transition to.
        /// </summary>
        public TState DestinationState { get; set; } = default!;

        /// <summary>
        /// Creates a new state transition.
        /// </summary>
        /// <param name="trigger">The trigger that causes this transition</param>
        /// <param name="destinationState">The destination state</param>
        public StateTransition(InternalProcessState trigger, TState destinationState)
        {
            Trigger = trigger;
            DestinationState = destinationState;
        }
    }
}
