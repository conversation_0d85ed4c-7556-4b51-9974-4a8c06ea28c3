using System.ComponentModel;

namespace shared.Components.InternalProcess.Enums
{
    /// <summary>
    /// Represents the overall internal process state for state machine transitions.
    /// </summary>
    public enum InternalProcessState
    {
        [Description("FINISHED")]
        Finished = 0,

        [Description("FAILED")]
        Failed = 1,

        [Description("SUCCEEDED")]
        Succeeded = 2,

        [Description("RETRY")]
        Retry = 3
    }
}
