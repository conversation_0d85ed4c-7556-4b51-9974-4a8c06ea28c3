using shared.Components.InternalProcess.Enums;
using shared.Components.InternalProcess.Examples;
using System.Text.Json;
using Xunit;

namespace shared.Components.InternalProcess.Tests
{
    /// <summary>
    /// Unit tests for the InternalProcessStateMachine.
    /// </summary>
    public class InternalProcessStateMachineTests
    {
        [Fact]
        public void Constructor_WithInitialStateAndPayload_SetsCorrectValues()
        {
            // Arrange
            var initialState = new SimpleState { Status = SimpleProcessState.Initial };
            var payload = new SimplePayload { Data = "test", Value = 42 };

            // Act
            var stateMachine = new InternalProcessStateMachine<SimpleState, SimplePayload>(
                initialState, payload, InternalProcessState.Finished);

            // Assert
            Assert.Equal(SimpleProcessState.Initial, stateMachine.CurrentState.Status);
            Assert.Equal(InternalProcessState.Finished, stateMachine.CurrentInternalProcessState);
            Assert.Equal("test", stateMachine.Payload.Data);
            Assert.Equal(42, stateMachine.Payload.Value);
        }

        [Fact]
        public void ForceState_UpdatesStateAndInternalProcessState()
        {
            // Arrange
            var stateMachine = CreateSimpleStateMachine();
            var newState = new SimpleState { Status = SimpleProcessState.Processing };

            // Act
            stateMachine.ForceState(newState, InternalProcessState.Retry);

            // Assert
            Assert.Equal(SimpleProcessState.Processing, stateMachine.CurrentState.Status);
            Assert.Equal(InternalProcessState.Retry, stateMachine.CurrentInternalProcessState);
        }

        [Fact]
        public void UpdatePayload_UpdatesPayloadCorrectly()
        {
            // Arrange
            var stateMachine = CreateSimpleStateMachine();
            var newPayload = new SimplePayload { Data = "updated", Value = 100 };

            // Act
            stateMachine.UpdatePayload(newPayload);

            // Assert
            Assert.Equal("updated", stateMachine.Payload.Data);
            Assert.Equal(100, stateMachine.Payload.Value);
        }

        [Fact]
        public void ConfigureState_AddsStateConfiguration()
        {
            // Arrange
            var stateMachine = CreateSimpleStateMachine();
            var state = new SimpleState { Status = SimpleProcessState.Initial };

            // Act
            var config = stateMachine.ConfigureState(state);

            // Assert
            Assert.NotNull(config);
            Assert.Equal(SimpleProcessState.Initial, config.State.Status);
        }

        [Fact]
        public void AddTransition_AddsValidTransition()
        {
            // Arrange
            var stateMachine = CreateSimpleStateMachine();
            var fromState = new SimpleState { Status = SimpleProcessState.Initial };
            var toState = new SimpleState { Status = SimpleProcessState.Processing };

            // Act
            stateMachine.ConfigureState(fromState)
                .AddTransition(InternalProcessState.Succeeded, toState);

            // Assert
            var transitions = stateMachine.GetValidTransitions();
            Assert.Single(transitions);
            Assert.Equal(InternalProcessState.Succeeded, transitions[0].Trigger);
            Assert.Equal(SimpleProcessState.Processing, transitions[0].DestinationState.Status);
        }

        [Fact]
        public void IsTransitionValid_WithValidTransition_ReturnsTrue()
        {
            // Arrange
            var stateMachine = CreateSimpleStateMachine();
            var fromState = new SimpleState { Status = SimpleProcessState.Initial };
            var toState = new SimpleState { Status = SimpleProcessState.Processing };

            stateMachine.ConfigureState(fromState)
                .AddTransition(InternalProcessState.Succeeded, toState);

            // Act
            bool isValid = stateMachine.IsTransitionValid(InternalProcessState.Succeeded);

            // Assert
            Assert.True(isValid);
        }

        [Fact]
        public void IsTransitionValid_WithInvalidTransition_ReturnsFalse()
        {
            // Arrange
            var stateMachine = CreateSimpleStateMachine();

            // Act
            bool isValid = stateMachine.IsTransitionValid(InternalProcessState.Failed);

            // Assert
            Assert.False(isValid);
        }

        [Fact]
        public async Task RunStateTask_WithConfiguredTask_ExecutesSuccessfully()
        {
            // Arrange
            var stateMachine = CreateSimpleStateMachine();
            var state = new SimpleState { Status = SimpleProcessState.Initial };
            bool taskExecuted = false;

            stateMachine.ConfigureState(state)
                .SetTask(async (payload, internalState) =>
                {
                    taskExecuted = true;
                    await Task.CompletedTask;
                    return InternalProcessState.Succeeded;
                });

            // Act
            var result = await stateMachine.RunStateTask();

            // Assert
            Assert.True(taskExecuted);
            Assert.Equal(InternalProcessState.Succeeded, result);
            Assert.Equal(InternalProcessState.Succeeded, stateMachine.CurrentInternalProcessState);
        }

        [Fact]
        public async Task RunStateTask_WithoutConfiguredTask_ThrowsException()
        {
            // Arrange
            var stateMachine = CreateSimpleStateMachine();

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => stateMachine.RunStateTask());
        }

        [Fact]
        public void TryTransition_WithValidTransition_ReturnsTrue()
        {
            // Arrange
            var stateMachine = CreateSimpleStateMachine();
            var fromState = new SimpleState { Status = SimpleProcessState.Initial };
            var toState = new SimpleState { Status = SimpleProcessState.Processing };

            stateMachine.ConfigureState(fromState)
                .AddTransition(InternalProcessState.Succeeded, toState);

            stateMachine.ForceState(fromState, InternalProcessState.Succeeded);

            // Act
            bool transitioned = stateMachine.TryTransition();

            // Assert
            Assert.True(transitioned);
            Assert.Equal(SimpleProcessState.Processing, stateMachine.CurrentState.Status);
        }

        [Fact]
        public void SetError_SetsErrorMessageAndFailedState()
        {
            // Arrange
            var stateMachine = CreateSimpleStateMachine();
            var errorMessage = "Test error";

            // Act
            stateMachine.SetError(errorMessage);

            // Assert
            Assert.Equal(errorMessage, stateMachine.StateData.ErrorMessage);
            Assert.Equal(InternalProcessState.Failed, stateMachine.CurrentInternalProcessState);
        }

        [Fact]
        public void IncrementRetry_IncrementsCountAndSetsRetryState()
        {
            // Arrange
            var stateMachine = CreateSimpleStateMachine();

            // Act
            stateMachine.IncrementRetry();

            // Assert
            Assert.Equal(1, stateMachine.StateData.RetryCount);
            Assert.Equal(InternalProcessState.Retry, stateMachine.CurrentInternalProcessState);
        }

        [Fact]
        public void StateData_IsSerializable()
        {
            // Arrange
            var stateMachine = CreateSimpleStateMachine();
            stateMachine.SetError("Test error");
            stateMachine.IncrementRetry();

            // Act
            var json = JsonSerializer.Serialize(stateMachine.StateData);
            var deserializedStateData = JsonSerializer.Deserialize<InternalProcessStateData<SimpleState, SimplePayload>>(json);

            // Assert
            Assert.NotNull(deserializedStateData);
            Assert.Equal(stateMachine.StateData.InternalProcessState, deserializedStateData.InternalProcessState);
            Assert.Equal(stateMachine.StateData.StatefulObject.Status, deserializedStateData.StatefulObject.Status);
            Assert.Equal(stateMachine.StateData.Payload.Data, deserializedStateData.Payload.Data);
            Assert.Equal(stateMachine.StateData.ErrorMessage, deserializedStateData.ErrorMessage);
            Assert.Equal(stateMachine.StateData.RetryCount, deserializedStateData.RetryCount);
        }

        [Fact]
        public void Constructor_WithStateData_RestoresCorrectly()
        {
            // Arrange
            var originalStateMachine = CreateSimpleStateMachine();
            originalStateMachine.SetError("Test error");
            originalStateMachine.IncrementRetry();
            var stateData = originalStateMachine.StateData;

            // Act
            var restoredStateMachine = new InternalProcessStateMachine<SimpleState, SimplePayload>(stateData);

            // Assert
            Assert.Equal(originalStateMachine.CurrentState.Status, restoredStateMachine.CurrentState.Status);
            Assert.Equal(originalStateMachine.CurrentInternalProcessState, restoredStateMachine.CurrentInternalProcessState);
            Assert.Equal(originalStateMachine.Payload.Data, restoredStateMachine.Payload.Data);
            Assert.Equal(originalStateMachine.StateData.ErrorMessage, restoredStateMachine.StateData.ErrorMessage);
            Assert.Equal(originalStateMachine.StateData.RetryCount, restoredStateMachine.StateData.RetryCount);
        }

        private static InternalProcessStateMachine<SimpleState, SimplePayload> CreateSimpleStateMachine()
        {
            var initialState = new SimpleState { Status = SimpleProcessState.Initial };
            var payload = new SimplePayload { Data = "test", Value = 42 };
            return new InternalProcessStateMachine<SimpleState, SimplePayload>(
                initialState, payload, InternalProcessState.Finished);
        }
    }
}
