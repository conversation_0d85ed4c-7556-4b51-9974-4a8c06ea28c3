using shared.Components.InternalProcess.Enums;
using shared.Models.Interfaces;

namespace shared.Components.InternalProcess.Examples
{
    /// <summary>
    /// Example state enum for demonstration purposes.
    /// </summary>
    public enum SimpleProcessState
    {
        Initial,
        Validating,
        Processing,
        Finalizing,
        Completed,
        Error
    }

    /// <summary>
    /// Example state object that implements IStateful.
    /// </summary>
    public class SimpleState : IStateful<SimpleProcessState>
    {
        public SimpleProcessState Status { get; set; } = SimpleProcessState.Initial;

        public override bool Equals(object? obj)
        {
            if (obj is SimpleState other)
            {
                return Status == other.Status;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return Status.GetHashCode();
        }

        public override string ToString()
        {
            return Status.ToString();
        }
    }

    /// <summary>
    /// Example payload object.
    /// </summary>
    public class SimplePayload
    {
        public string Data { get; set; } = string.Empty;
        public int Value { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public bool IsValid { get; set; } = true;
    }

    /// <summary>
    /// Example implementation showing how to use the InternalProcess state machine.
    /// </summary>
    public class SimpleProcessExample
    {
        /// <summary>
        /// Creates and configures a simple process state machine.
        /// </summary>
        /// <param name="payload">The payload to process</param>
        /// <returns>Configured state machine</returns>
        public static InternalProcessStateMachine<SimpleState, SimplePayload> CreateStateMachine(SimplePayload payload)
        {
            var initialState = new SimpleState { Status = SimpleProcessState.Initial };
            var stateMachine = new InternalProcessStateMachine<SimpleState, SimplePayload>(
                initialState, 
                payload, 
                InternalProcessState.Finished
            );

            // Configure Initial state
            stateMachine.ConfigureState(new SimpleState { Status = SimpleProcessState.Initial })
                .AddTransition(InternalProcessState.Succeeded, new SimpleState { Status = SimpleProcessState.Validating })
                .AddTransition(InternalProcessState.Failed, new SimpleState { Status = SimpleProcessState.Error })
                .SetTask(async (payload, currentInternalState) =>
                {
                    // Initialize the process
                    await Task.Delay(100); // Simulate work
                    return payload.IsValid ? InternalProcessState.Succeeded : InternalProcessState.Failed;
                });

            // Configure Validating state
            stateMachine.ConfigureState(new SimpleState { Status = SimpleProcessState.Validating })
                .AddTransition(InternalProcessState.Succeeded, new SimpleState { Status = SimpleProcessState.Processing })
                .AddTransition(InternalProcessState.Failed, new SimpleState { Status = SimpleProcessState.Error })
                .AddTransition(InternalProcessState.Retry, new SimpleState { Status = SimpleProcessState.Validating })
                .SetTask(async (payload, currentInternalState) =>
                {
                    // Validate the payload
                    await Task.Delay(200); // Simulate validation work
                    
                    if (string.IsNullOrEmpty(payload.Data))
                    {
                        return InternalProcessState.Failed;
                    }
                    
                    if (payload.Value < 0)
                    {
                        return InternalProcessState.Retry;
                    }
                    
                    return InternalProcessState.Succeeded;
                });

            // Configure Processing state
            stateMachine.ConfigureState(new SimpleState { Status = SimpleProcessState.Processing })
                .AddTransition(InternalProcessState.Succeeded, new SimpleState { Status = SimpleProcessState.Finalizing })
                .AddTransition(InternalProcessState.Failed, new SimpleState { Status = SimpleProcessState.Error })
                .SetTask(async (payload, currentInternalState) =>
                {
                    // Process the data
                    await Task.Delay(500); // Simulate processing work
                    
                    // Simulate random failure for demonstration
                    var random = new Random();
                    if (random.Next(1, 10) > 8) // 20% chance of failure
                    {
                        return InternalProcessState.Failed;
                    }
                    
                    return InternalProcessState.Succeeded;
                });

            // Configure Finalizing state
            stateMachine.ConfigureState(new SimpleState { Status = SimpleProcessState.Finalizing })
                .AddTransition(InternalProcessState.Succeeded, new SimpleState { Status = SimpleProcessState.Completed })
                .AddTransition(InternalProcessState.Failed, new SimpleState { Status = SimpleProcessState.Error })
                .SetTask(async (payload, currentInternalState) =>
                {
                    // Finalize the process
                    await Task.Delay(100); // Simulate finalization work
                    return InternalProcessState.Succeeded;
                });

            // Configure Completed state (terminal state)
            stateMachine.ConfigureState(new SimpleState { Status = SimpleProcessState.Completed })
                .SetTask(async (payload, currentInternalState) =>
                {
                    // Process is complete, no further action needed
                    await Task.CompletedTask;
                    return InternalProcessState.Finished;
                });

            // Configure Error state (terminal state)
            stateMachine.ConfigureState(new SimpleState { Status = SimpleProcessState.Error })
                .SetTask(async (payload, currentInternalState) =>
                {
                    // Handle error state, possibly log or cleanup
                    await Task.CompletedTask;
                    return InternalProcessState.Failed;
                });

            return stateMachine;
        }

        /// <summary>
        /// Demonstrates running the state machine to completion.
        /// </summary>
        /// <param name="payload">The payload to process</param>
        /// <returns>The final state data</returns>
        public static async Task<InternalProcessStateData<SimpleState, SimplePayload>> RunExample(SimplePayload payload)
        {
            var stateMachine = CreateStateMachine(payload);
            
            Console.WriteLine($"Starting process with payload: {payload.Data}, Value: {payload.Value}");
            
            // Run the state machine until completion
            int maxIterations = 10; // Prevent infinite loops
            int iteration = 0;
            
            while (iteration < maxIterations)
            {
                iteration++;
                Console.WriteLine($"Iteration {iteration}: Current State = {stateMachine.CurrentState.Status}, Internal State = {stateMachine.CurrentInternalProcessState}");
                
                try
                {
                    // Execute current state task and attempt transition
                    bool transitioned = await stateMachine.ExecuteAndTransition();
                    
                    if (!transitioned)
                    {
                        Console.WriteLine("No valid transition found. Process complete or stuck.");
                        break;
                    }
                    
                    // Check if we've reached a terminal state
                    if (stateMachine.CurrentState.Status == SimpleProcessState.Completed ||
                        stateMachine.CurrentState.Status == SimpleProcessState.Error)
                    {
                        Console.WriteLine($"Process reached terminal state: {stateMachine.CurrentState.Status}");
                        break;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error during execution: {ex.Message}");
                    stateMachine.SetError(ex.Message);
                    break;
                }
            }
            
            Console.WriteLine($"Final State: {stateMachine.CurrentState.Status}");
            Console.WriteLine($"Final Internal State: {stateMachine.CurrentInternalProcessState}");
            Console.WriteLine($"Retry Count: {stateMachine.StateData.RetryCount}");
            
            if (!string.IsNullOrEmpty(stateMachine.StateData.ErrorMessage))
            {
                Console.WriteLine($"Error Message: {stateMachine.StateData.ErrorMessage}");
            }
            
            return stateMachine.StateData;
        }
    }
}
