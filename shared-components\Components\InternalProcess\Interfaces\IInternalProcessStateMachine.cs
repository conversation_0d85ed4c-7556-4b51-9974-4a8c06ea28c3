using shared.Components.InternalProcess.Enums;
using shared.Components.InternalProcess.Models;
using shared.Models.Interfaces;

namespace shared.Components.InternalProcess.Interfaces
{
    /// <summary>
    /// Interface for the internal process state machine.
    /// </summary>
    /// <typeparam name="TState">The type of the state object that implements IStateful</typeparam>
    /// <typeparam name="TObject">The type of the payload object</typeparam>
    public interface IInternalProcessStateMachine<TState, TObject> where TState : IStateful<TState>
    {
        /// <summary>
        /// Gets the current state data of the state machine.
        /// </summary>
        InternalProcessStateData<TState, TObject> StateData { get; }

        /// <summary>
        /// Gets the current state of the stateful object.
        /// </summary>
        TState CurrentState { get; }

        /// <summary>
        /// Gets the current internal process state.
        /// </summary>
        InternalProcessState CurrentInternalProcessState { get; }

        /// <summary>
        /// Gets the current payload.
        /// </summary>
        TObject Payload { get; }

        /// <summary>
        /// Configures a state in the state machine.
        /// </summary>
        /// <param name="state">The state to configure</param>
        /// <returns>A state configuration object for method chaining</returns>
        StateConfiguration<TState, TObject> ConfigureState(TState state);

        /// <summary>
        /// Forces the state machine to a specific state and internal process state.
        /// </summary>
        /// <param name="newState">The new state</param>
        /// <param name="newInternalProcessState">The new internal process state</param>
        void ForceState(TState newState, InternalProcessState newInternalProcessState);

        /// <summary>
        /// Updates the payload object.
        /// </summary>
        /// <param name="newPayload">The new payload</param>
        void UpdatePayload(TObject newPayload);

        /// <summary>
        /// Runs the task associated with the current state.
        /// </summary>
        /// <returns>The new internal process state returned by the task</returns>
        Task<InternalProcessState> RunStateTask();

        /// <summary>
        /// Attempts to transition to a new state based on the current internal process state.
        /// </summary>
        /// <returns>True if a transition was made, false if no valid transition exists</returns>
        bool TryTransition();

        /// <summary>
        /// Executes the current state task and attempts to transition based on the result.
        /// </summary>
        /// <returns>True if a transition was made after task execution</returns>
        Task<bool> ExecuteAndTransition();

        /// <summary>
        /// Sets an error message and updates the internal process state to Failed.
        /// </summary>
        /// <param name="errorMessage">The error message</param>
        void SetError(string errorMessage);

        /// <summary>
        /// Increments the retry count and sets the internal process state to Retry.
        /// </summary>
        void IncrementRetry();

        /// <summary>
        /// Gets all valid transitions from the current state.
        /// </summary>
        /// <returns>List of valid transitions</returns>
        List<StateTransition<TState>> GetValidTransitions();

        /// <summary>
        /// Checks if a transition is valid from the current state with the given trigger.
        /// </summary>
        /// <param name="trigger">The trigger to check</param>
        /// <returns>True if the transition is valid</returns>
        bool IsTransitionValid(InternalProcessState trigger);
    }
}
