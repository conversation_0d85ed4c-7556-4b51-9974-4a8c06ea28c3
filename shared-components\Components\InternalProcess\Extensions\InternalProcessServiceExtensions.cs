using Microsoft.Extensions.DependencyInjection;
using shared.Components.InternalProcess.Interfaces;
using shared.Models.Interfaces;

namespace shared.Components.InternalProcess.Extensions
{
    /// <summary>
    /// Extension methods for registering InternalProcess services with dependency injection.
    /// </summary>
    public static class InternalProcessServiceExtensions
    {
        /// <summary>
        /// Adds InternalProcess state machine services to the service collection.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for method chaining</returns>
        public static IServiceCollection AddInternalProcessStateMachine(this IServiceCollection services)
        {
            // Register the state machine as transient since each process should have its own instance
            services.AddTransient(typeof(IInternalProcessStateMachine<,>), typeof(InternalProcessStateMachine<,>));
            
            return services;
        }

        /// <summary>
        /// Adds InternalProcess state machine services with a factory for creating instances.
        /// </summary>
        /// <typeparam name="TState">The type of the state object that implements IStateful</typeparam>
        /// <typeparam name="TObject">The type of the payload object</typeparam>
        /// <param name="services">The service collection</param>
        /// <param name="factory">Factory function to create state machine instances</param>
        /// <returns>The service collection for method chaining</returns>
        public static IServiceCollection AddInternalProcessStateMachine<TState, TObject>(
            this IServiceCollection services,
            Func<IServiceProvider, IInternalProcessStateMachine<TState, TObject>> factory)
            where TState : IStateful<TState>
        {
            services.AddTransient(factory);
            return services;
        }

        /// <summary>
        /// Adds InternalProcess state machine services with a specific implementation.
        /// </summary>
        /// <typeparam name="TState">The type of the state object that implements IStateful</typeparam>
        /// <typeparam name="TObject">The type of the payload object</typeparam>
        /// <typeparam name="TImplementation">The implementation type</typeparam>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for method chaining</returns>
        public static IServiceCollection AddInternalProcessStateMachine<TState, TObject, TImplementation>(
            this IServiceCollection services)
            where TState : IStateful<TState>
            where TImplementation : class, IInternalProcessStateMachine<TState, TObject>
        {
            services.AddTransient<IInternalProcessStateMachine<TState, TObject>, TImplementation>();
            return services;
        }
    }
}
