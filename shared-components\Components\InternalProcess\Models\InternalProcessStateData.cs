using Amazon.DynamoDBv2.DataModel;
using shared.Components.InternalProcess.Enums;
using shared.Converters;
using shared.Models.Interfaces;
using System.Text.Json.Serialization;

namespace shared.Components.InternalProcess.Models
{
    /// <summary>
    /// Serializable state data for the internal process state machine.
    /// Contains all necessary information to persist and restore state machine state.
    /// </summary>
    /// <typeparam name="TState">The type of the state object that implements IStateful</typeparam>
    /// <typeparam name="TObject">The type of the payload object</typeparam>
    public class InternalProcessStateData<TState, TObject>
        where TState : IStateful<TState>
    {
        /// <summary>
        /// The current internal process state (Finished, Failed, Succeeded, Retry).
        /// </summary>
        [DynamoDBProperty(typeof(DynamoEnumStringConverter<InternalProcessState>))]
        [JsonConverter(typeof(JsonEnumStringConverter<InternalProcessState>))]
        public InternalProcessState InternalProcessState { get; set; } = InternalProcessState.Finished;

        /// <summary>
        /// The stateful object that tracks the current state of the process.
        /// </summary>
        [DynamoDBProperty(typeof(DynamoDBJsonFallback<TState>))]
        public TState StatefulObject { get; set; } = default!;

        /// <summary>
        /// The actual payload object being processed.
        /// </summary>
        [DynamoDBProperty(typeof(DynamoDBJsonFallback<TObject>))]
        public TObject Payload { get; set; } = default!;

        /// <summary>
        /// Timestamp when the state data was last updated.
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Optional error message if the process failed.
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Number of retry attempts made.
        /// </summary>
        public int RetryCount { get; set; } = 0;
    }
}
