﻿using Amazon.BedrockAgent;
using Amazon.DynamoDBv2.DataModel;
using Amazon.IdentityManagement;
using Amazon.S3;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using platform.Constants;
using platform.Models.Configuration;
using platform.Services;
using shared.Components.ApiEventBus;
using shared.Controllers;

namespace platform.Controllers
{
    [Route(Routes.KnowledgeBaseController.BasePath)]
    [Produces("application/json")]
    public partial class KnowledgeBaseController : SuperController
    {
        private readonly ILogger<KnowledgeBaseController> logger;
        private readonly IAmazonS3 fileContext;
        private readonly IAmazonBedrockAgent bedrockAgent;
        private readonly IOptionsMonitor<BedrockConfiguration> kbConfiguration;
        private readonly IIAM iIAM;

        public KnowledgeBaseController(ILogger<KnowledgeBaseController> logger, IAmazonS3 fileContext, IAmazonBedrockAgent bedrockAgent, IApiEventBus apiEventBus, IDynamoDBContext dynamoDBContext, IOptionsMonitor<BedrockConfiguration> kbConfiguration, IIAM iIAM) : base(apiEventBus, dynamoDBContext)
        {
            this.fileContext = fileContext;
            this.logger = logger;
            this.bedrockAgent = bedrockAgent;
            this.kbConfiguration = kbConfiguration;
            this.iIAM = iIAM;
        }

    }
}
