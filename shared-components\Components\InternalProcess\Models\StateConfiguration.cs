using shared.Components.InternalProcess.Enums;
using shared.Models.Interfaces;

namespace shared.Components.InternalProcess.Models
{
    /// <summary>
    /// Configuration for a specific state in the state machine.
    /// </summary>
    /// <typeparam name="TState">The type of the state object that implements IStateful</typeparam>
    /// <typeparam name="TObject">The type of the payload object</typeparam>
    public class StateConfiguration<TState, TObject> where TState : IStateful<TState>
    {
        /// <summary>
        /// The state this configuration applies to.
        /// </summary>
        public TState State { get; set; } = default!;

        /// <summary>
        /// List of permitted transitions from this state.
        /// </summary>
        public List<StateTransition<TState>> Transitions { get; set; } = new();

        /// <summary>
        /// The task to execute when in this state.
        /// </summary>
        public Func<TObject, InternalProcessState, Task<InternalProcessState>>? StateTask { get; set; }

        /// <summary>
        /// Creates a new state configuration.
        /// </summary>
        /// <param name="state">The state this configuration applies to</param>
        public StateConfiguration(TState state)
        {
            State = state;
        }

        /// <summary>
        /// Adds a transition from this state.
        /// </summary>
        /// <param name="trigger">The trigger that causes the transition</param>
        /// <param name="destinationState">The destination state</param>
        /// <returns>This configuration for method chaining</returns>
        public StateConfiguration<TState, TObject> AddTransition(InternalProcessState trigger, TState destinationState)
        {
            Transitions.Add(new StateTransition<TState>(trigger, destinationState));
            return this;
        }

        /// <summary>
        /// Sets the task to execute when in this state.
        /// </summary>
        /// <param name="task">The task function</param>
        /// <returns>This configuration for method chaining</returns>
        public StateConfiguration<TState, TObject> SetTask(Func<TObject, InternalProcessState, Task<InternalProcessState>> task)
        {
            StateTask = task;
            return this;
        }
    }
}
